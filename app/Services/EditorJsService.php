<?php

namespace App\Services;

use EditorJS\EditorJS;
use EditorJS\EditorJSException;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use DOMDocument;
use DOMXPath;

class EditorJsService
{
    /**
     * Парсит контент EditorJS и возвращает массив отрендеренных блоков
     * */
    public static function render($content): string
    {
        try {
            $configJson = json_encode(config('editorjs.config') ?: []);

            $editor = new EditorJS($content, $configJson);

            $renderedBlocks = [];

            foreach ($editor->getBlocks() as $block) {

                $viewName = "blocks." . Str::snake($block['type'], '-');

                if (!View::exists($viewName)) {
                    $viewName = 'blocks.not-found';
                }

                $renderedBlocks[] = View::make($viewName, [
                    'type' => $block['type'],
                    'data' => $block['data']
                ])->render();
            }

            return implode($renderedBlocks);
        } catch (EditorJSException $e) {
            return "";
        }
    }

    /**
     * Преобразует HTML в JSON формат EditorJS
     */
    public static function parseHtml(string $html): array
    {
        if (empty(trim($html))) {
            return [
                'time' => time() * 1000,
                'blocks' => [],
                'version' => '2.28.2'
            ];
        }

        $dom = new DOMDocument();
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        $blocks = [];
        $body = $dom->getElementsByTagName('body')->item(0);

        if ($body) {
            foreach ($body->childNodes as $node) {
                if ($node->nodeType === XML_ELEMENT_NODE) {
                    $block = self::parseNode($node, $xpath);
                    if ($block) {
                        $blocks[] = $block;
                    }
                }
            }
        }

        return [
            'time' => time() * 1000,
            'blocks' => $blocks,
            'version' => '2.28.2'
        ];
    }

    /**
     * Парсит отдельный HTML узел в блок EditorJS
     */
    private static function parseNode($node, DOMXPath $xpath): ?array
    {
        $tagName = strtolower($node->tagName);

        switch ($tagName) {
            case 'p':
                return self::parseParagraph($node);

            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
                return self::parseHeader($node);

            case 'ul':
            case 'ol':
                return self::parseList($node);

            case 'figure':
                return self::parseFigure($node, $xpath);

            case 'img':
                return self::parseImage($node);

            case 'blockquote':
                return self::parseQuote($node);

            case 'pre':
                return self::parseCode($node);

            case 'table':
                return self::parseTable($node);

            case 'hr':
                return self::parseDelimiter();

            case 'div':
                return self::parseDiv($node, $xpath);

            default:
                // Для неизвестных тегов создаем raw блок
                return self::parseRaw($node);
        }
    }

    /**
     * Парсит параграф
     */
    private static function parseParagraph($node): array
    {
        return [
            'id' => self::generateId(),
            'type' => 'paragraph',
            'data' => [
                'text' => self::getInnerHTML($node)
            ]
        ];
    }

    /**
     * Парсит заголовок
     */
    private static function parseHeader($node): array
    {
        $level = (int) substr($node->tagName, 1);

        return [
            'id' => self::generateId(),
            'type' => 'header',
            'data' => [
                'text' => self::getInnerHTML($node),
                'level' => $level
            ]
        ];
    }

    /**
     * Парсит список
     */
    private static function parseList($node): array
    {
        $style = strtolower($node->tagName) === 'ul' ? 'unordered' : 'ordered';
        $items = [];

        foreach ($node->getElementsByTagName('li') as $li) {
            $items[] = self::getInnerHTML($li);
        }

        return [
            'id' => self::generateId(),
            'type' => 'list',
            'data' => [
                'style' => $style,
                'items' => $items
            ]
        ];
    }

    /**
     * Парсит figure (изображение с подписью)
     */
    private static function parseFigure($node, DOMXPath $xpath): ?array
    {
        $img = $xpath->query('.//img', $node)->item(0);
        if (!$img) {
            return null;
        }

        $caption = '';
        $captionNode = $xpath->query('.//footer[@class="image-caption"]', $node)->item(0);
        if ($captionNode) {
            $caption = trim($captionNode->textContent);
        }

        $classes = $node->getAttribute('class');

        return [
            'id' => self::generateId(),
            'type' => 'image',
            'data' => [
                'file' => [
                    'url' => $img->getAttribute('src')
                ],
                'caption' => $caption,
                'withBorder' => strpos($classes, 'image--bordered') !== false,
                'withBackground' => strpos($classes, 'image--backgrounded') !== false,
                'stretched' => strpos($classes, 'image--stretched') !== false
            ]
        ];
    }

    /**
     * Парсит отдельное изображение
     */
    private static function parseImage($node): array
    {
        return [
            'id' => self::generateId(),
            'type' => 'image',
            'data' => [
                'file' => [
                    'url' => $node->getAttribute('src')
                ],
                'caption' => $node->getAttribute('alt') ?: '',
                'withBorder' => false,
                'withBackground' => false,
                'stretched' => false
            ]
        ];
    }

    /**
     * Парсит цитату
     */
    private static function parseQuote($node): array
    {
        $text = '';
        $caption = '';

        // Ищем cite элемент для caption
        $cite = $node->getElementsByTagName('cite')->item(0);
        if ($cite) {
            $caption = trim($cite->textContent);
            $cite->parentNode->removeChild($cite);
        }

        $text = self::getInnerHTML($node);

        return [
            'id' => self::generateId(),
            'type' => 'quote',
            'data' => [
                'text' => $text,
                'caption' => $caption,
                'alignment' => 'left'
            ]
        ];
    }

    /**
     * Парсит код
     */
    private static function parseCode($node): array
    {
        $code = $node->getElementsByTagName('code')->item(0);
        $codeText = $code ? $code->textContent : $node->textContent;

        return [
            'id' => self::generateId(),
            'type' => 'code',
            'data' => [
                'code' => $codeText
            ]
        ];
    }

    /**
     * Парсит таблицу
     */
    private static function parseTable($node): array
    {
        $content = [];
        $withHeadings = false;

        $thead = $node->getElementsByTagName('thead')->item(0);
        $tbody = $node->getElementsByTagName('tbody')->item(0);

        if ($thead) {
            $withHeadings = true;
            $headerRow = $thead->getElementsByTagName('tr')->item(0);
            if ($headerRow) {
                $row = [];
                foreach ($headerRow->getElementsByTagName('th') as $th) {
                    $row[] = self::getInnerHTML($th);
                }
                $content[] = $row;
            }
        }

        $rows = $tbody ? $tbody->getElementsByTagName('tr') : $node->getElementsByTagName('tr');
        foreach ($rows as $tr) {
            $row = [];
            foreach ($tr->getElementsByTagName('td') as $td) {
                $row[] = self::getInnerHTML($td);
            }
            if (!empty($row)) {
                $content[] = $row;
            }
        }

        return [
            'id' => self::generateId(),
            'type' => 'table',
            'data' => [
                'withHeadings' => $withHeadings,
                'content' => $content
            ]
        ];
    }

    /**
     * Парсит разделитель
     */
    private static function parseDelimiter(): array
    {
        return [
            'id' => self::generateId(),
            'type' => 'delimiter',
            'data' => []
        ];
    }

    /**
     * Парсит div элемент
     */
    private static function parseDiv($node, DOMXPath $xpath): ?array
    {
        $class = $node->getAttribute('class');

        // Проверяем, является ли это чеклистом
        if (strpos($class, 'checklist') !== false) {
            return self::parseChecklist($node, $xpath);
        }

        // Проверяем, является ли это embed
        if (strpos($class, 'embed') !== false) {
            return self::parseEmbed($node);
        }

        // Иначе создаем raw блок
        return self::parseRaw($node);
    }

    /**
     * Парсит чеклист
     */
    private static function parseChecklist($node, DOMXPath $xpath): array
    {
        $items = [];
        $checkboxes = $xpath->query('.//input[@type="checkbox"]', $node);

        foreach ($checkboxes as $checkbox) {
            $checked = $checkbox->hasAttribute('checked');
            $text = '';

            // Ищем текст рядом с чекбоксом
            $nextSibling = $checkbox->nextSibling;
            while ($nextSibling) {
                if ($nextSibling->nodeType === XML_TEXT_NODE) {
                    $text .= trim($nextSibling->textContent);
                } elseif ($nextSibling->nodeType === XML_ELEMENT_NODE) {
                    $text .= self::getInnerHTML($nextSibling);
                }
                $nextSibling = $nextSibling->nextSibling;
            }

            $items[] = [
                'text' => trim($text),
                'checked' => $checked
            ];
        }

        return [
            'id' => self::generateId(),
            'type' => 'checklist',
            'data' => [
                'items' => $items
            ]
        ];
    }

    /**
     * Парсит embed
     */
    private static function parseEmbed($node): array
    {
        $iframe = $node->getElementsByTagName('iframe')->item(0);
        $source = $iframe ? $iframe->getAttribute('src') : '';

        return [
            'id' => self::generateId(),
            'type' => 'embed',
            'data' => [
                'service' => 'custom',
                'source' => $source,
                'embed' => self::getOuterHTML($node),
                'width' => 580,
                'height' => 320,
                'caption' => ''
            ]
        ];
    }

    /**
     * Парсит raw HTML
     */
    private static function parseRaw($node): array
    {
        return [
            'id' => self::generateId(),
            'type' => 'raw',
            'data' => [
                'html' => self::getOuterHTML($node)
            ]
        ];
    }

    /**
     * Получает внутренний HTML элемента
     */
    private static function getInnerHTML($node): string
    {
        $innerHTML = '';
        foreach ($node->childNodes as $child) {
            $innerHTML .= $node->ownerDocument->saveHTML($child);
        }
        return trim($innerHTML);
    }

    /**
     * Получает внешний HTML элемента
     */
    private static function getOuterHTML($node): string
    {
        return $node->ownerDocument->saveHTML($node);
    }

    /**
     * Генерирует уникальный ID для блока
     */
    private static function generateId(): string
    {
        return uniqid();
    }
}
