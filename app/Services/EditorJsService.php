<?php

namespace App\Services;

use EditorJS\EditorJS;
use EditorJS\EditorJSException;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class EditorJsService
{

    /**
     * Парсит контент EditorJS и возвращает массив отрендеренных блоков
     * */
    public static function render($content): string
    {
        try {
            $configJson = json_encode(config('editorjs.config') ?: []);

            $editor = new EditorJS($content, $configJson);

            $renderedBlocks = [];

            foreach ($editor->getBlocks() as $block) {

                $viewName = "blocks." . Str::snake($block['type'], '-');

                if (!View::exists($viewName)) {
                    $viewName = 'blocks.not-found';
                }

                $renderedBlocks[] = View::make($viewName, [
                    'type' => $block['type'],
                    'data' => $block['data']
                ])->render();
            }

            return implode($renderedBlocks);
        } catch (EditorJSException $e) {
            return "";
        }
    }
}
