<?php

namespace App\Services;

use EditorJS\EditorJS;
use EditorJS\EditorJSException;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use DOMDocument;
use DOMXPath;

class EditorJsService
{
    /**
     * Парсит контент EditorJS и возвращает массив отрендеренных блоков
     * */
    public static function render($content): string
    {
        try {
            $configJson = json_encode(config('editorjs.config') ?: []);

            $editor = new EditorJS($content, $configJson);

            $renderedBlocks = [];

            foreach ($editor->getBlocks() as $block) {

                $viewName = "blocks." . Str::snake($block['type'], '-');

                if (!View::exists($viewName)) {
                    $viewName = 'blocks.not-found';
                }

                $renderedBlocks[] = View::make($viewName, [
                    'type' => $block['type'],
                    'data' => $block['data']
                ])->render();
            }

            return implode($renderedBlocks);
        } catch (EditorJSException $e) {
            return "";
        }
    }

    /**
     * Упрощенная версия преобразования HTML в JSON формат EditorJS
     */
    public static function parseHtml(string $html): array
    {
        if (empty(trim($html))) {
            return [
                'time' => time() * 1000,
                'blocks' => [],
                'version' => '2.28.2'
            ];
        }

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        $blocks = [];
        $body = $dom->getElementsByTagName('body')->item(0) ?: $dom;

        foreach ($body->childNodes as $node) {
            if ($node->nodeType === XML_ELEMENT_NODE) {
                $block = self::parseSimpleNode($node);
                if ($block) {
                    $blocks[] = $block;
                }
            }
        }

        return [
            'time' => time() * 1000,
            'blocks' => $blocks,
            'version' => '2.28.2'
        ];
    }

    /**
     * Упрощенный парсинг HTML узла
     */
    private static function parseSimpleNode($node): ?array
    {
        $tagName = strtolower($node->tagName);

        switch ($tagName) {
            case 'p':
                return [
                    'id' => uniqid(),
                    'type' => 'paragraph',
                    'data' => ['text' => self::getTextContent($node)]
                ];

            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
                return [
                    'id' => uniqid(),
                    'type' => 'header',
                    'data' => [
                        'text' => self::getTextContent($node),
                        'level' => (int) substr($tagName, 1)
                    ]
                ];

            case 'ul':
            case 'ol':
                $items = [];
                foreach ($node->getElementsByTagName('li') as $li) {
                    $items[] = self::getTextContent($li);
                }
                return [
                    'id' => uniqid(),
                    'type' => 'list',
                    'data' => [
                        'style' => $tagName === 'ul' ? 'unordered' : 'ordered',
                        'items' => $items
                    ]
                ];

            case 'img':
                return [
                    'id' => uniqid(),
                    'type' => 'image',
                    'data' => [
                        'file' => ['url' => $node->getAttribute('src')],
                        'caption' => $node->getAttribute('alt') ?: '',
                        'withBorder' => false,
                        'withBackground' => false,
                        'stretched' => false
                    ]
                ];

            case 'blockquote':
                return [
                    'id' => uniqid(),
                    'type' => 'quote',
                    'data' => [
                        'text' => self::getTextContent($node),
                        'caption' => '',
                        'alignment' => 'left'
                    ]
                ];

            case 'pre':
                $code = $node->getElementsByTagName('code')->item(0);
                return [
                    'id' => uniqid(),
                    'type' => 'code',
                    'data' => [
                        'code' => $code ? $code->textContent : $node->textContent
                    ]
                ];

            case 'hr':
                return [
                    'id' => uniqid(),
                    'type' => 'delimiter',
                    'data' => []
                ];

            default:
                // Для остальных тегов создаем параграф с содержимым
                $text = self::getTextContent($node);
                if (!empty(trim($text))) {
                    return [
                        'id' => uniqid(),
                        'type' => 'paragraph',
                        'data' => ['text' => $text]
                    ];
                }
                return null;
        }
    }

    /**
     * Получает текстовое содержимое с сохранением базовой разметки
     */
    private static function getTextContent($node): string
    {
        $innerHTML = '';
        foreach ($node->childNodes as $child) {
            if ($child->nodeType === XML_TEXT_NODE) {
                $innerHTML .= $child->textContent;
            } elseif ($child->nodeType === XML_ELEMENT_NODE) {
                $tagName = strtolower($child->tagName);
                // Сохраняем только базовые теги форматирования
                if (in_array($tagName, ['b', 'strong', 'i', 'em', 'u', 'a', 'code', 'mark'])) {
                    $innerHTML .= $node->ownerDocument->saveHTML($child);
                } else {
                    $innerHTML .= $child->textContent;
                }
            }
        }
        return trim($innerHTML);
    }
}
