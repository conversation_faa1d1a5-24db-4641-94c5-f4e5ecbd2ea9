<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'cover_image' => $this->cover_image,
            'content' => $this->body,
            'excerpt' => $this->excerpt,
            'published_at' => $this->published_at,
            'rejection_reason' => $this->rejection_reason,
            'views_count' => $this->views_count,
            'read_time' => $this->read_time,
            'is_featured' => $this->is_featured,
            'allow_comments' => $this->allow_comments,
            'user' => UserPublicResource::make($this->whenLoaded('user')),
            'category' => ArticleCategory::make($this->whenLoaded('category')),
        ];
    }
}
