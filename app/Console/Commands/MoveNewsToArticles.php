<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\ArticleCategory;
use App\Models\News;
use Illuminate\Console\Command;

class MoveNewsToArticles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:move-news-to-articles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $news = News::get();

        $this->withProgressBar($news, function (News $news) {
            $this->makeArticle($news);
        });
    }

    private function makeArticle(News $news)
    {
        $article = Article::where('slug', $news->slug)->first();

        if ($article) {
            Article::create([
                'slug' => $news->slug,
                'user_id' => 4,
                'category_id' => 20,
                'title' => $news->title,
                'cover_image' => $news->image,
                'status' => 'published',
                'content' => $news->content,
                'excerpt' => $news->description,
                'published_at' => $news->published_at,
                'created_at' => $news->created_at,
                'updated_at' => $news->updated_at,
            ]);
        } else {
            $article->update([
                'user_id' => 4,
                'category_id' => 20,
                'title' => $news->title,
                'cover_image' => $news->image,
                'status' => 'published',
                'content' => $news->content,
                'excerpt' => $news->description,
                'published_at' => $news->published_at,
                'created_at' => $news->created_at,
                'updated_at' => $news->updated_at,
            ]);
        }
    }
}
